# Adaptive Bar Chart - TradingView Lightweight Charts (NPM Version)

A professional Node.js application using TradingView's Lightweight Charts library for real-time visualization of adaptive volume-based market data.

## Features

- **Professional Trading Interface**: Built with TradingView Lightweight Charts
- **Real-time WebSocket Connection**: Connects to `ws://localhost:8080/ws/adaptive-bars`
- **Rolling Data Simulation**: See bars updating in real-time
- **Sample Data Testing**: Test chart functionality before connecting real data
- **Market Metrics**: Live price, volume, trade count, and VWAP display
- **Market Conditions**: Shows volatility, TPS, and dynamic thresholds
- **Connection Management**: Auto-reconnection and status monitoring
- **Responsive Design**: Works on desktop and mobile devices
- **Dark Theme**: Professional trading interface styling

## Quick Start

### Prerequisites
- Node.js (v16 or higher)
- npm or yarn

### Installation

1. **Navigate to the project directory**:
   ```bash
   cd chart-ui-npm
   ```

2. **Install dependencies**:
   ```bash
   npm install
   ```

3. **Start the development server**:
   ```bash
   npm run dev
   ```

4. **Open your browser**:
   - The application will automatically open at `http://localhost:3000`
   - Or manually visit `http://localhost:3000`

### Alternative Commands

```bash
# Start development server without auto-opening browser
npm start

# Build for production
npm run build
```

## Testing the Chart

### 1. Sample Data
- Click **"Add Sample Data"** to see 6 static candlesticks
- Verify the chart displays proper OHLC bars

### 2. Rolling Data Simulation
- Click **"Start Rolling Data"** to see bars updating every second
- Watch realistic price movements with proper candlestick formation
- Click **"Stop Rolling Data"** to pause the simulation

### 3. Real WebSocket Data
- Start your Java backend on port 8080
- Ensure the WebSocket endpoint `/ws/adaptive-bars` is running
- The chart will automatically connect and display real-time data

## Project Structure

```
chart-ui-npm/
├── src/
│   ├── index.js          # Main application logic
│   ├── index.html        # HTML template
│   └── styles.css        # CSS styling
├── package.json          # Dependencies and scripts
├── webpack.config.js     # Webpack configuration
└── README.md            # This file
```

## Key Features Explained

### Rolling Data Simulation
The "Start Rolling Data" feature demonstrates:
- **Real-time Updates**: New bars appear every second
- **Realistic Price Movement**: Simulated volatility and price changes
- **Proper OHLC Formation**: Each bar shows open, high, low, close
- **Live Metrics**: Price changes, volume, and trade count updates

### WebSocket Integration
- **Auto-connection**: Attempts to connect on startup
- **Auto-reconnection**: Reconnects automatically if connection drops
- **Message Handling**: Processes different message types (ADAPTIVE_BAR, WELCOME, ERROR)
- **Status Monitoring**: Visual connection status indicator

### Chart Features
- **Professional Appearance**: TradingView-style interface
- **Interactive Controls**: Zoom, pan, crosshair
- **Color Coding**: Green for bullish, red for bearish candles
- **Time Scale**: Shows time and date information
- **Price Scale**: Right-side price axis with proper formatting

## WebSocket Data Format

The application expects WebSocket messages in this format:

```json
{
  "type": "ADAPTIVE_BAR",
  "timestamp": "2024-01-01T12:00:00Z",
  "symbol": "BTCUSDT",
  "data": {
    "openTime": "2024-01-01T12:00:00Z",
    "closeTime": "2024-01-01T12:01:00Z",
    "symbol": "BTCUSDT",
    "openPrice": 50000,
    "highPrice": 50100,
    "lowPrice": 49900,
    "closePrice": 50050,
    "volumeWeightedAveragePrice": 50025,
    "totalVolumeTraded": 1000,
    "aggressiveBuyVolume": 600,
    "aggressiveSellVolume": 400,
    "tradeCount": 25,
    "closingReason": "ADAPTIVE_VOLUME_THRESHOLD",
    "marketConditionSnapshot": {
      "rollingVolatility": 0.001234,
      "tradesPerSecond": 15.5,
      "recentVolumeRate": 40.2,
      "dynamicVolumeThreshold": 800,
      "baseVolumeThreshold": 1000,
      "volatilityAdjustment": 0.8,
      "tpsAdjustment": 0.9,
      "volumeRateAdjustment": 1.1
    }
  }
}
```

## Configuration

### WebSocket URL
Change the WebSocket URL in `src/index.js`:
```javascript
const wsUrl = 'ws://localhost:8080/ws/adaptive-bars';
```

### Chart Styling
Modify chart colors and appearance in the `createChart()` method:
```javascript
this.candlestickSeries = this.chart.addCandlestickSeries({
    upColor: '#089981',      // Bullish candle color
    downColor: '#f23645',    // Bearish candle color
    // ... other styling options
});
```

### Development Server Port
Change the port in `webpack.config.js`:
```javascript
devServer: {
    port: 3000, // Change this port
    // ...
}
```

## Troubleshooting

### Installation Issues
```bash
# Clear npm cache
npm cache clean --force

# Delete node_modules and reinstall
rm -rf node_modules package-lock.json
npm install
```

### Chart Not Displaying
1. Check browser console for errors
2. Ensure all dependencies are installed
3. Verify webpack dev server is running

### WebSocket Connection Issues
1. Check if Java backend is running on port 8080
2. Verify WebSocket endpoint is accessible
3. Check browser network tab for connection errors

### Performance Issues
1. Use production build: `npm run build`
2. Reduce rolling data frequency in `startRollingData()`
3. Limit the number of bars kept in memory

## Browser Support

- Chrome (recommended)
- Firefox
- Safari
- Edge

## Dependencies

- **lightweight-charts**: TradingView's official charting library
- **webpack**: Module bundler for development and production builds
- **webpack-dev-server**: Development server with hot reloading

## License

This project uses TradingView Lightweight Charts library which is licensed under Apache License 2.0.
