{"name": "adaptive-bar-chart", "version": "1.0.0", "description": "Real-time adaptive volume bar chart using TradingView Lightweight Charts", "main": "src/index.js", "scripts": {"start": "webpack serve --mode development", "build": "webpack --mode production", "dev": "webpack serve --mode development --open"}, "keywords": ["trading", "charts", "candlestick", "websocket", "real-time"], "author": "Trading Bot", "license": "MIT", "dependencies": {"lightweight-charts": "^4.2.0"}, "devDependencies": {"webpack": "^5.89.0", "webpack-cli": "^5.1.4", "webpack-dev-server": "^4.15.1", "html-webpack-plugin": "^5.5.3", "css-loader": "^6.8.1", "style-loader": "^3.3.3"}}