<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Adaptive Bar Chart - TradingView Lightweight Charts</title>
</head>
<body>
    <div class="header">
        <h1>Adaptive Volume Bar Chart</h1>
        <p class="subtitle">Real-time candlestick visualization using TradingView Lightweight Charts</p>
    </div>

    <div class="container">
        <div class="controls">
            <div class="connection-status">
                <span class="status-indicator" id="statusIndicator"></span>
                <span id="connectionStatus">Disconnected</span>
            </div>
            <div class="button-group">
                <button class="btn btn-danger" id="clearBtn">Clear Chart</button>
            </div>
        </div>

        <div class="chart-container">
            <div class="chart-header">
                <h2 class="chart-title" id="chartTitle">Adaptive Volume Bars</h2>
                <div class="chart-info">
                    <span id="barCount">0 bars</span>
                </div>
            </div>
            <div class="chart-wrapper" id="chartContainer"></div>
        </div>

        <div class="metrics-grid" id="metricsGrid" style="display: none;">
            <div class="metric-card">
                <div class="metric-label">Last Price</div>
                <div class="metric-value" id="lastPrice">--</div>
            </div>
            <div class="metric-card">
                <div class="metric-label">Change</div>
                <div class="metric-value" id="priceChange">--</div>
            </div>
            <div class="metric-card">
                <div class="metric-label">Volume</div>
                <div class="metric-value" id="volume">--</div>
            </div>
            <div class="metric-card">
                <div class="metric-label">Trades</div>
                <div class="metric-value" id="tradeCount">--</div>
            </div>
            <div class="metric-card">
                <div class="metric-label">VWAP</div>
                <div class="metric-value" id="vwap">--</div>
            </div>
            <div class="metric-card">
                <div class="metric-label">Bars Count</div>
                <div class="metric-value" id="totalBars">0</div>
            </div>
        </div>

        <div class="market-conditions" id="marketConditions" style="display: none;">
            <h3>Market Conditions</h3>
            <div class="conditions-grid" id="conditionsGrid">
                <!-- Market conditions will be populated here -->
            </div>
        </div>

        <div class="error-message" id="errorMessage" style="display: none;">
            <strong>Error:</strong> <span id="errorText"></span>
        </div>

        <div class="log-container">
            <h4>Connection Log</h4>
            <div id="logEntries"></div>
        </div>
    </div>
</body>
</html>
