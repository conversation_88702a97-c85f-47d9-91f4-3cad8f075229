/* TradingView-inspired styling */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', sans-serif;
    background-color: #131722;
    color: #d1d4dc;
    overflow-x: hidden;
}

.container {
    max-width: 100%;
    margin: 0 auto;
    padding: 20px;
}

.header {
    background-color: #1e222d;
    padding: 20px;
    border-bottom: 1px solid #2a2e39;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.3);
    margin-bottom: 20px;
}

.header h1 {
    color: #ffffff;
    font-size: 28px;
    font-weight: 700;
    text-align: center;
    margin-bottom: 8px;
}

.subtitle {
    color: #868993;
    font-size: 14px;
    text-align: center;
    font-weight: 400;
}

.controls {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
    padding: 15px 20px;
    background-color: #1e222d;
    border-radius: 8px;
    border: 1px solid #2a2e39;
}

.connection-status {
    display: flex;
    align-items: center;
    gap: 10px;
    font-size: 14px;
}

.status-indicator {
    width: 12px;
    height: 12px;
    border-radius: 50%;
    background-color: #f23645;
    transition: background-color 0.3s;
}

.status-indicator.connected {
    background-color: #089981;
}

.button-group {
    display: flex;
    gap: 10px;
}

.btn {
    border: none;
    padding: 10px 20px;
    border-radius: 6px;
    cursor: pointer;
    font-size: 14px;
    font-weight: 500;
    transition: all 0.3s;
}

.btn-primary {
    background-color: #2962ff;
    color: white;
}

.btn-primary:hover {
    background-color: #1e53e5;
}

.btn-secondary {
    background-color: #089981;
    color: white;
}

.btn-secondary:hover {
    background-color: #067d6a;
}

.btn-danger {
    background-color: #f23645;
    color: white;
}

.btn-danger:hover {
    background-color: #d1294b;
}

.chart-container {
    background-color: #1e222d;
    border-radius: 8px;
    padding: 20px;
    margin-bottom: 20px;
    border: 1px solid #2a2e39;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
}

.chart-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
}

.chart-title {
    color: #ffffff;
    font-size: 20px;
    font-weight: 600;
}

.chart-info {
    color: #868993;
    font-size: 14px;
}

.chart-wrapper {
    width: 100%;
    height: 600px;
    background-color: #131722;
    border-radius: 6px;
    border: 1px solid #2a2e39;
}

.metrics-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 15px;
    margin-bottom: 20px;
}

.metric-card {
    background-color: #2a2e39;
    padding: 15px;
    border-radius: 6px;
    border-left: 4px solid #2962ff;
}

.metric-label {
    font-size: 12px;
    color: #868993;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    margin-bottom: 5px;
}

.metric-value {
    font-size: 18px;
    font-weight: 600;
    color: #ffffff;
}

.metric-value.positive {
    color: #089981;
}

.metric-value.negative {
    color: #f23645;
}

.market-conditions {
    background-color: #1e222d;
    border-radius: 8px;
    padding: 20px;
    border: 1px solid #2a2e39;
    margin-bottom: 20px;
}

.market-conditions h3 {
    color: #ffffff;
    margin-bottom: 15px;
    font-size: 18px;
}

.conditions-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 15px;
}

.condition-item {
    display: flex;
    justify-content: space-between;
    padding: 10px 0;
    border-bottom: 1px solid #2a2e39;
}

.condition-label {
    font-size: 12px;
    color: #868993;
    text-transform: uppercase;
}

.condition-value {
    font-size: 14px;
    color: #ffffff;
    font-weight: 500;
}

.error-message {
    background-color: #2d1b1b;
    border: 1px solid #f23645;
    border-radius: 6px;
    padding: 15px;
    margin: 20px 0;
    color: #f23645;
}

.log-container {
    background-color: #1e222d;
    border-radius: 8px;
    padding: 15px;
    border: 1px solid #2a2e39;
    max-height: 200px;
    overflow-y: auto;
}

.log-container h4 {
    color: #ffffff;
    margin-bottom: 10px;
    font-size: 14px;
}

.log-entry {
    font-size: 12px;
    color: #868993;
    margin-bottom: 5px;
    font-family: 'Courier New', monospace;
}

.log-entry.success {
    color: #089981;
}

.log-entry.error {
    color: #f23645;
}

.log-entry.info {
    color: #2962ff;
}

@media (max-width: 768px) {
    .controls {
        flex-direction: column;
        gap: 15px;
    }
    
    .chart-wrapper {
        height: 400px;
    }
    
    .button-group {
        flex-wrap: wrap;
    }
}
