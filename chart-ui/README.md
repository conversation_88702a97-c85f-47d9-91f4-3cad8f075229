# Adaptive Bar Chart - TradingView Lightweight Charts

A pure JavaScript implementation using TradingView's Lightweight Charts library for real-time visualization of adaptive volume-based market data.

## Features

- **Professional Trading Interface**: Uses TradingView's Lightweight Charts for authentic trading experience
- **Real-time WebSocket Connection**: Connects to `ws://localhost:8080/ws/adaptive-bars`
- **Candlestick Charts**: Proper OHLC visualization with bullish/bearish colors
- **Market Metrics**: Live price, volume, trade count, and VWAP display
- **Market Conditions**: Shows volatility, TPS, and dynamic thresholds
- **Connection Management**: Auto-reconnection and status monitoring
- **Sample Data**: Test button to verify chart functionality
- **Responsive Design**: Works on desktop and mobile devices
- **Dark Theme**: Professional trading interface styling

## Quick Start

1. **Open the HTML file**:
   ```bash
   # Simply open index.html in your browser
   # Or serve it with a simple HTTP server:
   python -m http.server 8000
   # Then visit: http://localhost:8000
   ```

2. **Test the chart**:
   - Click "Add Sample Data" to see sample candlesticks
   - Click "Clear Chart" to reset

3. **Connect to real data**:
   - Start your Java backend on port 8080
   - Ensure the WebSocket endpoint `/ws/adaptive-bars` is running
   - The chart will automatically connect and display real-time data

## File Structure

```
chart-ui/
├── index.html          # Main HTML page
├── app.js             # JavaScript application logic
└── README.md          # This file
```

## WebSocket Data Format

The application expects WebSocket messages in this format:

```json
{
  "type": "ADAPTIVE_BAR",
  "timestamp": "2024-01-01T12:00:00Z",
  "symbol": "BTCUSDT",
  "data": {
    "openTime": "2024-01-01T12:00:00Z",
    "closeTime": "2024-01-01T12:01:00Z",
    "symbol": "BTCUSDT",
    "openPrice": 50000,
    "highPrice": 50100,
    "lowPrice": 49900,
    "closePrice": 50050,
    "volumeWeightedAveragePrice": 50025,
    "totalVolumeTraded": 1000,
    "aggressiveBuyVolume": 600,
    "aggressiveSellVolume": 400,
    "tradeCount": 25,
    "closingReason": "ADAPTIVE_VOLUME_THRESHOLD",
    "marketConditionSnapshot": {
      "rollingVolatility": 0.001234,
      "tradesPerSecond": 15.5,
      "recentVolumeRate": 40.2,
      "dynamicVolumeThreshold": 800,
      "baseVolumeThreshold": 1000,
      "volatilityAdjustment": 0.8,
      "tpsAdjustment": 0.9,
      "volumeRateAdjustment": 1.1
    }
  }
}
```

## Chart Features

### TradingView Lightweight Charts Benefits:
- **High Performance**: Optimized for financial data
- **Professional Appearance**: Matches real trading platforms
- **Smooth Animations**: Fluid chart updates
- **Interactive Tools**: Zoom, pan, crosshair
- **Mobile Friendly**: Touch-optimized controls

### Styling:
- **Dark Theme**: Professional trading interface
- **Color Coding**: Green for bullish, red for bearish candles
- **Grid Lines**: Subtle grid for better readability
- **Time Scale**: Shows time and date information
- **Price Scale**: Right-side price axis

## Configuration

### WebSocket URL:
Change the WebSocket URL in `app.js`:
```javascript
const wsUrl = 'ws://localhost:8080/ws/adaptive-bars';
```

### Chart Styling:
Modify chart colors and appearance in the `createChart()` method:
```javascript
this.candlestickSeries = this.chart.addCandlestickSeries({
    upColor: '#089981',      // Bullish candle color
    downColor: '#f23645',    // Bearish candle color
    // ... other styling options
});
```

## Browser Support

- Chrome (recommended)
- Firefox
- Safari
- Edge

## Advantages over Angular/ApexCharts

1. **Faster Loading**: No framework overhead
2. **Better Performance**: TradingView charts are optimized for financial data
3. **Smaller Bundle**: Single HTML file with CDN dependencies
4. **Professional Look**: Authentic trading platform appearance
5. **Easier Deployment**: Just serve static files

## Troubleshooting

### Chart Not Displaying:
1. Check browser console for JavaScript errors
2. Ensure TradingView library is loaded from CDN
3. Verify container element exists

### WebSocket Connection Issues:
1. Check if Java backend is running on port 8080
2. Verify WebSocket endpoint is accessible
3. Check browser network tab for connection errors

### No Data Appearing:
1. Test with sample data first
2. Check WebSocket message format
3. Verify data parsing in browser console

## License

This project uses TradingView Lightweight Charts library which is licensed under Apache License 2.0.
