// TradingView Lightweight Charts Application
class AdaptiveBarChart {
    constructor() {
        this.chart = null;
        this.candlestickSeries = null;
        this.websocket = null;
        this.isConnected = false;
        this.currentSymbol = null;
        this.lastPrice = null;
        this.candleData = [];
        
        this.init();
    }

    init() {
        this.createChart();
        this.connectWebSocket();
        this.log('Application initialized');
    }

    createChart() {
        const container = document.getElementById('chartContainer');
        
        // Create the chart with TradingView styling
        this.chart = LightweightCharts.createChart(container, {
            width: container.clientWidth,
            height: 600,
            layout: {
                background: {
                    type: 'solid',
                    color: '#131722'
                },
                textColor: '#d1d4dc',
            },
            grid: {
                vertLines: {
                    color: '#2a2e39',
                    style: 1,
                    visible: true,
                },
                horzLines: {
                    color: '#2a2e39',
                    style: 1,
                    visible: true,
                },
            },
            crosshair: {
                mode: LightweightCharts.CrosshairMode.Normal,
            },
            rightPriceScale: {
                borderColor: '#2a2e39',
                textColor: '#d1d4dc',
            },
            timeScale: {
                borderColor: '#2a2e39',
                textColor: '#d1d4dc',
                timeVisible: true,
                secondsVisible: true,
            },
        });

        // Create candlestick series
        this.candlestickSeries = this.chart.addCandlestickSeries({
            upColor: '#089981',
            downColor: '#f23645',
            borderDownColor: '#f23645',
            borderUpColor: '#089981',
            wickDownColor: '#f23645',
            wickUpColor: '#089981',
        });

        // Handle window resize
        window.addEventListener('resize', () => {
            this.chart.applyOptions({ width: container.clientWidth });
        });

        this.log('Chart created successfully');
    }

    connectWebSocket() {
        const wsUrl = 'ws://localhost:8080/ws/adaptive-bars';
        
        try {
            this.websocket = new WebSocket(wsUrl);
            
            this.websocket.onopen = (event) => {
                this.isConnected = true;
                this.updateConnectionStatus();
                this.log('WebSocket connected successfully');
            };

            this.websocket.onmessage = (event) => {
                try {
                    const message = JSON.parse(event.data);
                    this.handleWebSocketMessage(message);
                } catch (error) {
                    this.showError('Error parsing WebSocket message: ' + error.message);
                    this.log('Parse error: ' + error.message);
                }
            };

            this.websocket.onclose = (event) => {
                this.isConnected = false;
                this.updateConnectionStatus();
                this.log(`WebSocket disconnected: ${event.code} - ${event.reason}`);
                
                // Attempt to reconnect after 3 seconds
                setTimeout(() => {
                    if (!this.isConnected) {
                        this.log('Attempting to reconnect...');
                        this.connectWebSocket();
                    }
                }, 3000);
            };

            this.websocket.onerror = (error) => {
                this.showError('WebSocket connection error');
                this.log('WebSocket error: ' + error);
            };

        } catch (error) {
            this.showError('Failed to create WebSocket connection: ' + error.message);
            this.log('Connection error: ' + error.message);
        }
    }

    handleWebSocketMessage(message) {
        this.log(`Received message type: ${message.type}`);
        
        switch (message.type) {
            case 'ADAPTIVE_BAR':
                if (message.data) {
                    this.addAdaptiveBar(message.data);
                }
                break;
                
            case 'WELCOME':
                if (message.data) {
                    this.handleWelcomeMessage(message.data);
                }
                break;
                
            case 'ERROR':
                this.showError(message.data);
                break;
                
            case 'CONFIRMATION':
                this.log('Server confirmation: ' + message.data);
                break;
                
            default:
                this.log('Unknown message type: ' + message.type);
        }
    }

    addAdaptiveBar(barData) {
        // Convert to TradingView format
        const candleData = {
            time: Math.floor(new Date(barData.closeTime).getTime() / 1000), // Convert to seconds
            open: parseFloat(barData.openPrice),
            high: parseFloat(barData.highPrice),
            low: parseFloat(barData.lowPrice),
            close: parseFloat(barData.closePrice)
        };

        // Add to series
        this.candlestickSeries.update(candleData);
        this.candleData.push(candleData);
        
        // Update metrics
        this.updateMetrics(barData);
        
        // Update chart title
        this.currentSymbol = barData.symbol;
        document.getElementById('chartTitle').textContent = `${barData.symbol} - Adaptive Volume Bars`;
        
        this.log(`Added bar: ${barData.symbol} O:${barData.openPrice} H:${barData.highPrice} L:${barData.lowPrice} C:${barData.closePrice}`);
    }

    updateMetrics(barData) {
        const metricsGrid = document.getElementById('metricsGrid');
        metricsGrid.style.display = 'grid';
        
        // Calculate price change
        const currentPrice = parseFloat(barData.closePrice);
        const priceChange = this.lastPrice ? currentPrice - this.lastPrice : 0;
        const priceChangePercent = this.lastPrice ? (priceChange / this.lastPrice) * 100 : 0;
        
        // Update metrics display
        document.getElementById('lastPrice').textContent = currentPrice.toFixed(2);
        
        const priceChangeElement = document.getElementById('priceChange');
        const sign = priceChange >= 0 ? '+' : '';
        priceChangeElement.textContent = `${sign}${priceChange.toFixed(2)} (${sign}${priceChangePercent.toFixed(2)}%)`;
        priceChangeElement.className = `metric-value ${priceChange >= 0 ? 'positive' : 'negative'}`;
        
        document.getElementById('volume').textContent = this.formatVolume(parseFloat(barData.totalVolumeTraded));
        document.getElementById('tradeCount').textContent = barData.tradeCount;
        document.getElementById('vwap').textContent = parseFloat(barData.volumeWeightedAveragePrice).toFixed(2);
        
        this.lastPrice = currentPrice;
    }

    handleWelcomeMessage(welcomeData) {
        this.log('Welcome message received: ' + welcomeData.message);
        
        if (welcomeData.currentMarketConditions) {
            this.updateMarketConditions(welcomeData.currentMarketConditions);
        }
    }

    updateMarketConditions(conditions) {
        const marketConditions = document.getElementById('marketConditions');
        const conditionsGrid = document.getElementById('conditionsGrid');
        
        marketConditions.style.display = 'block';
        
        conditionsGrid.innerHTML = `
            <div class="condition-item">
                <span class="condition-label">Volatility</span>
                <span class="condition-value">${conditions.rollingVolatility?.toFixed(6) || 'N/A'}</span>
            </div>
            <div class="condition-item">
                <span class="condition-label">TPS</span>
                <span class="condition-value">${conditions.tradesPerSecond?.toFixed(2) || 'N/A'}</span>
            </div>
            <div class="condition-item">
                <span class="condition-label">Volume Rate</span>
                <span class="condition-value">${conditions.recentVolumeRate?.toFixed(2) || 'N/A'}</span>
            </div>
            <div class="condition-item">
                <span class="condition-label">Dynamic Threshold</span>
                <span class="condition-value">${conditions.dynamicVolumeThreshold?.toFixed(2) || 'N/A'}</span>
            </div>
            <div class="condition-item">
                <span class="condition-label">Base Threshold</span>
                <span class="condition-value">${conditions.baseVolumeThreshold?.toFixed(2) || 'N/A'}</span>
            </div>
            <div class="condition-item">
                <span class="condition-label">Vol Adjustment</span>
                <span class="condition-value">${conditions.volatilityAdjustment?.toFixed(3) || 'N/A'}</span>
            </div>
        `;
    }

    updateConnectionStatus() {
        const statusIndicator = document.getElementById('statusIndicator');
        const connectionStatus = document.getElementById('connectionStatus');
        
        if (this.isConnected) {
            statusIndicator.classList.add('connected');
            connectionStatus.textContent = 'Connected';
        } else {
            statusIndicator.classList.remove('connected');
            connectionStatus.textContent = 'Disconnected';
        }
    }

    formatVolume(volume) {
        if (volume >= 1000000) {
            return (volume / 1000000).toFixed(2) + 'M';
        } else if (volume >= 1000) {
            return (volume / 1000).toFixed(2) + 'K';
        }
        return volume.toFixed(2);
    }

    showError(message) {
        const errorElement = document.getElementById('errorMessage');
        const errorText = document.getElementById('errorText');
        
        errorText.textContent = message;
        errorElement.style.display = 'block';
        
        // Hide error after 5 seconds
        setTimeout(() => {
            errorElement.style.display = 'none';
        }, 5000);
    }

    log(message) {
        const logEntries = document.getElementById('logEntries');
        const timestamp = new Date().toLocaleTimeString();
        const logEntry = document.createElement('div');
        logEntry.className = 'log-entry';
        logEntry.textContent = `[${timestamp}] ${message}`;
        
        logEntries.appendChild(logEntry);
        logEntries.scrollTop = logEntries.scrollHeight;
        
        // Keep only last 50 log entries
        while (logEntries.children.length > 50) {
            logEntries.removeChild(logEntries.firstChild);
        }
    }

    addSampleData() {
        const now = Math.floor(Date.now() / 1000);
        const sampleData = [
            { time: now - 300, open: 100, high: 105, low: 95, close: 102 },
            { time: now - 240, open: 102, high: 108, low: 100, close: 106 },
            { time: now - 180, open: 106, high: 110, low: 104, close: 108 },
            { time: now - 120, open: 108, high: 112, low: 106, close: 110 },
            { time: now - 60, open: 110, high: 115, low: 108, close: 113 },
            { time: now, open: 113, high: 118, low: 111, close: 116 }
        ];

        this.candlestickSeries.setData(sampleData);
        this.candleData = sampleData;
        
        // Show metrics with sample data
        document.getElementById('metricsGrid').style.display = 'grid';
        document.getElementById('lastPrice').textContent = '116.00';
        document.getElementById('priceChange').textContent = '***** (*****%)';
        document.getElementById('priceChange').className = 'metric-value positive';
        document.getElementById('volume').textContent = '1.25K';
        document.getElementById('tradeCount').textContent = '25';
        document.getElementById('vwap').textContent = '108.50';
        
        document.getElementById('chartTitle').textContent = 'SAMPLE - Adaptive Volume Bars';
        
        this.log('Sample data added to chart');
    }

    clearChart() {
        this.candlestickSeries.setData([]);
        this.candleData = [];
        this.lastPrice = null;
        
        document.getElementById('metricsGrid').style.display = 'none';
        document.getElementById('marketConditions').style.display = 'none';
        document.getElementById('chartTitle').textContent = 'Adaptive Volume Bars';
        
        this.log('Chart cleared');
    }
}

// Global functions for buttons
function addSampleData() {
    if (window.chartApp) {
        window.chartApp.addSampleData();
    }
}

function clearChart() {
    if (window.chartApp) {
        window.chartApp.clearChart();
    }
}

// Initialize the application when the page loads
document.addEventListener('DOMContentLoaded', () => {
    window.chartApp = new AdaptiveBarChart();
});
