<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Adaptive Bar Chart - TradingView Lightweight Charts</title>
    <script src="https://unpkg.com/lightweight-charts/dist/lightweight-charts.standalone.production.js"></script>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', sans-serif;
            background-color: #131722;
            color: #d1d4dc;
            overflow-x: hidden;
        }

        .container {
            max-width: 100%;
            margin: 0 auto;
            padding: 20px;
        }

        .header {
            background-color: #1e222d;
            padding: 20px;
            border-bottom: 1px solid #2a2e39;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.3);
            margin-bottom: 20px;
        }

        .header h1 {
            color: #ffffff;
            font-size: 28px;
            font-weight: 700;
            text-align: center;
            margin-bottom: 8px;
        }

        .subtitle {
            color: #868993;
            font-size: 14px;
            text-align: center;
            font-weight: 400;
        }

        .controls {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
            padding: 15px 20px;
            background-color: #1e222d;
            border-radius: 8px;
            border: 1px solid #2a2e39;
        }

        .connection-status {
            display: flex;
            align-items: center;
            gap: 10px;
            font-size: 14px;
        }

        .status-indicator {
            width: 12px;
            height: 12px;
            border-radius: 50%;
            background-color: #f23645;
            transition: background-color 0.3s;
        }

        .status-indicator.connected {
            background-color: #089981;
        }

        .test-button {
            background-color: #2962ff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 6px;
            cursor: pointer;
            font-size: 14px;
            font-weight: 500;
            transition: background-color 0.3s;
        }

        .test-button:hover {
            background-color: #1e53e5;
        }

        .clear-button {
            background-color: #f23645;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 6px;
            cursor: pointer;
            font-size: 14px;
            font-weight: 500;
            transition: background-color 0.3s;
            margin-left: 10px;
        }

        .clear-button:hover {
            background-color: #d1294b;
        }

        .chart-container {
            background-color: #1e222d;
            border-radius: 8px;
            padding: 20px;
            margin-bottom: 20px;
            border: 1px solid #2a2e39;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
        }

        .chart-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
        }

        .chart-title {
            color: #ffffff;
            font-size: 20px;
            font-weight: 600;
        }

        .chart-wrapper {
            width: 100%;
            height: 600px;
            background-color: #131722;
            border-radius: 6px;
            border: 1px solid #2a2e39;
        }

        .metrics-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin-top: 20px;
        }

        .metric-card {
            background-color: #2a2e39;
            padding: 15px;
            border-radius: 6px;
            border-left: 4px solid #2962ff;
        }

        .metric-label {
            font-size: 12px;
            color: #868993;
            text-transform: uppercase;
            letter-spacing: 0.5px;
            margin-bottom: 5px;
        }

        .metric-value {
            font-size: 18px;
            font-weight: 600;
            color: #ffffff;
        }

        .metric-value.positive {
            color: #089981;
        }

        .metric-value.negative {
            color: #f23645;
        }

        .market-conditions {
            background-color: #1e222d;
            border-radius: 8px;
            padding: 20px;
            border: 1px solid #2a2e39;
        }

        .market-conditions h3 {
            color: #ffffff;
            margin-bottom: 15px;
            font-size: 18px;
        }

        .conditions-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 15px;
        }

        .condition-item {
            display: flex;
            justify-content: space-between;
            padding: 10px 0;
            border-bottom: 1px solid #2a2e39;
        }

        .condition-label {
            font-size: 12px;
            color: #868993;
            text-transform: uppercase;
        }

        .condition-value {
            font-size: 14px;
            color: #ffffff;
            font-weight: 500;
        }

        .error-message {
            background-color: #2d1b1b;
            border: 1px solid #f23645;
            border-radius: 6px;
            padding: 15px;
            margin: 20px 0;
            color: #f23645;
        }

        .log-container {
            background-color: #1e222d;
            border-radius: 8px;
            padding: 15px;
            margin-top: 20px;
            border: 1px solid #2a2e39;
            max-height: 200px;
            overflow-y: auto;
        }

        .log-container h4 {
            color: #ffffff;
            margin-bottom: 10px;
            font-size: 14px;
        }

        .log-entry {
            font-size: 12px;
            color: #868993;
            margin-bottom: 5px;
            font-family: 'Courier New', monospace;
        }

        @media (max-width: 768px) {
            .controls {
                flex-direction: column;
                gap: 15px;
            }
            
            .chart-wrapper {
                height: 400px;
            }
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>Adaptive Volume Bar Chart</h1>
        <p class="subtitle">Real-time candlestick visualization using TradingView Lightweight Charts</p>
    </div>

    <div class="container">
        <div class="controls">
            <div class="connection-status">
                <span class="status-indicator" id="statusIndicator"></span>
                <span id="connectionStatus">Disconnected</span>
            </div>
            <div>
                <button class="test-button" onclick="addSampleData()">Add Sample Data</button>
                <button class="clear-button" onclick="clearChart()">Clear Chart</button>
            </div>
        </div>

        <div class="chart-container">
            <div class="chart-header">
                <h2 class="chart-title" id="chartTitle">Adaptive Volume Bars</h2>
            </div>
            <div class="chart-wrapper" id="chartContainer"></div>
        </div>

        <div class="metrics-grid" id="metricsGrid" style="display: none;">
            <div class="metric-card">
                <div class="metric-label">Last Price</div>
                <div class="metric-value" id="lastPrice">--</div>
            </div>
            <div class="metric-card">
                <div class="metric-label">Change</div>
                <div class="metric-value" id="priceChange">--</div>
            </div>
            <div class="metric-card">
                <div class="metric-label">Volume</div>
                <div class="metric-value" id="volume">--</div>
            </div>
            <div class="metric-card">
                <div class="metric-label">Trades</div>
                <div class="metric-value" id="tradeCount">--</div>
            </div>
            <div class="metric-card">
                <div class="metric-label">VWAP</div>
                <div class="metric-value" id="vwap">--</div>
            </div>
        </div>

        <div class="market-conditions" id="marketConditions" style="display: none;">
            <h3>Market Conditions</h3>
            <div class="conditions-grid" id="conditionsGrid">
                <!-- Market conditions will be populated here -->
            </div>
        </div>

        <div class="error-message" id="errorMessage" style="display: none;">
            <strong>Error:</strong> <span id="errorText"></span>
        </div>

        <div class="log-container">
            <h4>Connection Log</h4>
            <div id="logEntries"></div>
        </div>
    </div>

    <script src="app.js"></script>
</body>
</html>
