<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>TradingView Test</title>
    <script src="https://unpkg.com/lightweight-charts@4.2.0/dist/lightweight-charts.standalone.production.js"></script>
    <style>
        body {
            margin: 0;
            padding: 20px;
            background-color: #131722;
            color: white;
            font-family: Arial, sans-serif;
        }
        #chart {
            width: 100%;
            height: 400px;
            background-color: #1e222d;
            border-radius: 8px;
        }
        button {
            background-color: #2962ff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 6px;
            cursor: pointer;
            margin: 10px 5px;
        }
    </style>
</head>
<body>
    <h1>TradingView Lightweight Charts Test</h1>
    <div>
        <button onclick="addSampleData()">Add Sample Data</button>
        <button onclick="clearChart()">Clear Chart</button>
    </div>
    <div id="chart"></div>

    <script>
        let chart;
        let candlestickSeries;

        function initChart() {
            const container = document.getElementById('chart');
            
            console.log('LightweightCharts object:', LightweightCharts);
            console.log('Available methods:', Object.keys(LightweightCharts));
            
            chart = LightweightCharts.createChart(container, {
                width: container.clientWidth,
                height: 400,
                layout: {
                    background: {
                        type: 'solid',
                        color: '#131722'
                    },
                    textColor: '#d1d4dc',
                },
                grid: {
                    vertLines: {
                        color: '#2a2e39',
                    },
                    horzLines: {
                        color: '#2a2e39',
                    },
                },
                timeScale: {
                    borderColor: '#2a2e39',
                },
                rightPriceScale: {
                    borderColor: '#2a2e39',
                },
            });

            console.log('Chart object:', chart);
            console.log('Chart methods:', Object.keys(chart));

            // Try different method names
            if (typeof chart.addCandlestickSeries === 'function') {
                console.log('Using addCandlestickSeries');
                candlestickSeries = chart.addCandlestickSeries({
                    upColor: '#089981',
                    downColor: '#f23645',
                    borderDownColor: '#f23645',
                    borderUpColor: '#089981',
                    wickDownColor: '#f23645',
                    wickUpColor: '#089981',
                });
            } else if (typeof chart.addSeries === 'function') {
                console.log('Using addSeries with candlestick type');
                candlestickSeries = chart.addSeries('candlestick', {
                    upColor: '#089981',
                    downColor: '#f23645',
                    borderDownColor: '#f23645',
                    borderUpColor: '#089981',
                    wickDownColor: '#f23645',
                    wickUpColor: '#089981',
                });
            } else {
                console.error('No candlestick method found!');
                return;
            }

            console.log('Candlestick series created:', candlestickSeries);
        }

        function addSampleData() {
            if (!candlestickSeries) {
                console.error('Candlestick series not initialized');
                return;
            }

            const now = Math.floor(Date.now() / 1000);
            const sampleData = [
                { time: now - 300, open: 100, high: 105, low: 95, close: 102 },
                { time: now - 240, open: 102, high: 108, low: 100, close: 106 },
                { time: now - 180, open: 106, high: 110, low: 104, close: 108 },
                { time: now - 120, open: 108, high: 112, low: 106, close: 110 },
                { time: now - 60, open: 110, high: 115, low: 108, close: 113 },
                { time: now, open: 113, high: 118, low: 111, close: 116 }
            ];

            console.log('Setting sample data:', sampleData);
            candlestickSeries.setData(sampleData);
        }

        function clearChart() {
            if (candlestickSeries) {
                candlestickSeries.setData([]);
            }
        }

        // Initialize when page loads
        document.addEventListener('DOMContentLoaded', initChart);
    </script>
</body>
</html>
