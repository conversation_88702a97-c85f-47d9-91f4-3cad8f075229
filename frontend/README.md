# Adaptive Bar Chart - Angular Frontend

This Angular application provides real-time visualization of adaptive volume-based market data using ApexCharts candlestick charts. It connects to the Java WebSocket endpoint to stream live trading data.

## Features

- **Real-time Candlestick Charts**: Live OHLC data visualization using ApexCharts
- **WebSocket Integration**: Connects to `ws://localhost:8080/ws/adaptive-bars`
- **Market Metrics Display**: Shows price, volume, trade count, and VWAP
- **Market Conditions**: Displays volatility, TPS, and dynamic thresholds
- **Responsive Design**: Works on desktop and mobile devices
- **Dark Theme**: Styled similar to backtesting.py library
- **Auto-reconnection**: Automatically reconnects on WebSocket disconnection

## Prerequisites

- Node.js (v18 or higher)
- npm or yarn
- Java backend running on port 8080 with WebSocket endpoint

## Installation

1. Navigate to the frontend directory:
```bash
cd frontend
```

2. Install dependencies:
```bash
npm install
```

## Development

Start the development server:
```bash
npm start
```

The application will be available at `http://localhost:4200`

## Build

Build the project for production:
```bash
npm run build
```

The build artifacts will be stored in the `dist/` directory.

## Project Structure

```
frontend/
├── src/
│   ├── app/
│   │   ├── components/
│   │   │   └── candlestick-chart.component.ts
│   │   ├── models/
│   │   │   └── websocket-message.model.ts
│   │   ├── services/
│   │   │   ├── websocket.service.ts
│   │   │   └── chart.service.ts
│   │   └── app.component.ts
│   ├── styles.scss
│   ├── index.html
│   └── main.ts
├── package.json
├── angular.json
└── README.md
```

## WebSocket Data Format

The application expects WebSocket messages in this format:

```json
{
  "type": "ADAPTIVE_BAR",
  "timestamp": "2024-01-01T12:00:00Z",
  "symbol": "BTCUSDT",
  "data": {
    "openTime": "2024-01-01T12:00:00Z",
    "closeTime": "2024-01-01T12:01:00Z",
    "symbol": "BTCUSDT",
    "openPrice": 50000,
    "highPrice": 50100,
    "lowPrice": 49900,
    "closePrice": 50050,
    "volumeWeightedAveragePrice": 50025,
    "totalVolumeTraded": 1000,
    "aggressiveBuyVolume": 600,
    "aggressiveSellVolume": 400,
    "tradeCount": 25,
    "closingReason": "ADAPTIVE_VOLUME_THRESHOLD",
    "marketConditionSnapshot": {
      "rollingVolatility": 0.001234,
      "tradesPerSecond": 15.5,
      "recentVolumeRate": 40.2,
      "dynamicVolumeThreshold": 800,
      "baseVolumeThreshold": 1000,
      "volatilityAdjustment": 0.8,
      "tpsAdjustment": 0.9,
      "volumeRateAdjustment": 1.1
    }
  }
}
```

## Configuration

### WebSocket URL
The WebSocket URL is configured in `src/app/services/websocket.service.ts`:
```typescript
private readonly wsUrl = 'ws://localhost:8080/ws/adaptive-bars';
```

### Chart Settings
Chart configuration can be modified in `src/app/services/chart.service.ts`:
- `maxDataPoints`: Number of candlesticks to keep in memory (default: 100)
- Chart colors, themes, and styling options

## Troubleshooting

### WebSocket Connection Issues
1. Ensure the Java backend is running on port 8080
2. Check that the WebSocket endpoint `/ws/adaptive-bars` is accessible
3. Verify CORS settings allow connections from `localhost:4200`

### Chart Not Updating
1. Check browser console for WebSocket connection errors
2. Verify that the backend is sending data in the expected format
3. Ensure the `AdaptiveVolumeBarAggregator` is running and producing bars

### Performance Issues
1. Reduce `maxDataPoints` in chart service if handling large amounts of data
2. Consider implementing data throttling for high-frequency updates

## Dependencies

- **Angular 17**: Modern Angular framework
- **ApexCharts**: Powerful charting library
- **ng-apexcharts**: Angular wrapper for ApexCharts
- **RxJS**: Reactive programming for data streams

## Browser Support

- Chrome (recommended)
- Firefox
- Safari
- Edge

## License

This project is part of the trading bot system and follows the same licensing terms.
