import { Component } from '@angular/core';
import { CommonModule } from '@angular/common';
import { CandlestickChartComponent } from './components/candlestick-chart.component';

@Component({
  selector: 'app-root',
  standalone: true,
  imports: [CommonModule, CandlestickChartComponent],
  template: `
    <div class="app-container">
      <header class="header">
        <h1>Adaptive Volume Bar Chart</h1>
        <p class="subtitle">Real-time candlestick visualization of adaptive volume-based market data</p>
      </header>
      
      <main class="main-content">
        <app-candlestick-chart></app-candlestick-chart>
      </main>
      
      <footer class="footer">
        <p>&copy; 2024 Trading Bot - Adaptive Bar Visualization</p>
      </footer>
    </div>
  `,
  styles: [`
    .app-container {
      min-height: 100vh;
      display: flex;
      flex-direction: column;
      background-color: #1a1a1a;
    }

    .header {
      background-color: #2d2d2d;
      padding: 20px;
      border-bottom: 2px solid #007acc;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.3);
    }

    .header h1 {
      margin: 0;
      color: #ffffff;
      font-size: 28px;
      font-weight: 700;
      text-align: center;
    }

    .subtitle {
      margin: 8px 0 0 0;
      color: #cccccc;
      font-size: 14px;
      text-align: center;
      font-weight: 400;
    }

    .main-content {
      flex: 1;
      padding: 0 20px;
      max-width: 1400px;
      margin: 0 auto;
      width: 100%;
    }

    .footer {
      background-color: #2d2d2d;
      padding: 15px 20px;
      border-top: 1px solid #404040;
      text-align: center;
      color: #888888;
      font-size: 12px;
    }

    @media (max-width: 768px) {
      .header {
        padding: 15px;
      }
      
      .header h1 {
        font-size: 24px;
      }
      
      .main-content {
        padding: 0 10px;
      }
    }
  `]
})
export class AppComponent {
  title = 'Adaptive Bar Chart';
}
