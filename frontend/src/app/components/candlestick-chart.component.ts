import { Component, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, ViewChild } from '@angular/core';
import { CommonModule } from '@angular/common';
import { ChartComponent, NgApexchartsModule } from 'ng-apexcharts';
import { Subject, takeUntil } from 'rxjs';
import { WebSocketService } from '../services/websocket.service';
import { ChartService, ChartOptions } from '../services/chart.service';
import { ChartMetrics, WelcomeData } from '../models/websocket-message.model';

@Component({
  selector: 'app-candlestick-chart',
  standalone: true,
  imports: [CommonModule, NgApexchartsModule],
  template: `
    <div class="chart-container">
      <div class="chart-header">
        <h2 class="chart-title">{{ currentSymbol || 'Adaptive Volume Bars' }}</h2>
        <div class="header-controls">
          <button class="test-button" (click)="addSampleData()">Add Sample Data</button>
          <div class="connection-status">
            <span class="status-indicator" [class.connected]="isConnected"></span>
            <span>{{ isConnected ? 'Connected' : 'Disconnected' }}</span>
          </div>
        </div>
      </div>

      <div class="metrics-bar" *ngIf="metrics">
        <div class="metric-item">
          <span class="metric-label">Last Price</span>
          <span class="metric-value" [class]="getPriceChangeClass()">
            {{ metrics.lastPrice.toFixed(2) }}
          </span>
        </div>
        <div class="metric-item">
          <span class="metric-label">Change</span>
          <span class="metric-value" [class]="getPriceChangeClass()">
            {{ formatPriceChange() }}
          </span>
        </div>
        <div class="metric-item">
          <span class="metric-label">Volume</span>
          <span class="metric-value">{{ formatVolume() }}</span>
        </div>
        <div class="metric-item">
          <span class="metric-label">Trades</span>
          <span class="metric-value">{{ metrics.tradeCount }}</span>
        </div>
        <div class="metric-item">
          <span class="metric-label">VWAP</span>
          <span class="metric-value">{{ metrics.vwap.toFixed(2) }}</span>
        </div>
      </div>

      <div class="chart-wrapper">
        <apx-chart
          #chart
          [series]="chartOptions.series"
          [chart]="chartOptions.chart"
          [xaxis]="chartOptions.xaxis"
          [yaxis]="chartOptions.yaxis"
          [tooltip]="chartOptions.tooltip"
          [theme]="chartOptions.theme"
          [grid]="chartOptions.grid"
          [plotOptions]="chartOptions.plotOptions">
        </apx-chart>
      </div>

      <div class="welcome-info" *ngIf="welcomeData">
        <h3>Market Conditions</h3>
        <div class="condition-grid">
          <div class="condition-item">
            <span class="condition-label">Volatility</span>
            <span class="condition-value">
              {{ welcomeData.currentMarketConditions?.rollingVolatility?.toFixed(6) || 'N/A' }}
            </span>
          </div>
          <div class="condition-item">
            <span class="condition-label">TPS</span>
            <span class="condition-value">
              {{ welcomeData.currentMarketConditions?.tradesPerSecond?.toFixed(2) || 'N/A' }}
            </span>
          </div>
          <div class="condition-item">
            <span class="condition-label">Volume Rate</span>
            <span class="condition-value">
              {{ welcomeData.currentMarketConditions?.recentVolumeRate?.toFixed(2) || 'N/A' }}
            </span>
          </div>
          <div class="condition-item">
            <span class="condition-label">Dynamic Threshold</span>
            <span class="condition-value">
              {{ welcomeData.currentMarketConditions?.dynamicVolumeThreshold?.toFixed(2) || 'N/A' }}
            </span>
          </div>
        </div>
      </div>
    </div>

    <div class="error-message" *ngIf="errorMessage">
      <strong>Error:</strong> {{ errorMessage }}
    </div>
  `,
  styles: [`
    .chart-container {
      background-color: #2d2d2d;
      border-radius: 8px;
      padding: 20px;
      margin: 20px 0;
      box-shadow: 0 4px 8px rgba(0, 0, 0, 0.3);
    }

    .chart-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 20px;
    }

    .header-controls {
      display: flex;
      align-items: center;
      gap: 15px;
    }

    .test-button {
      background-color: #007acc;
      color: white;
      border: none;
      padding: 8px 16px;
      border-radius: 4px;
      cursor: pointer;
      font-size: 12px;
      transition: background-color 0.3s;
    }

    .test-button:hover {
      background-color: #005a9e;
    }

    .chart-title {
      color: #ffffff;
      font-size: 24px;
      font-weight: 600;
      margin: 0;
    }

    .connection-status {
      display: flex;
      align-items: center;
      gap: 8px;
      font-size: 14px;
      color: #cccccc;
    }

    .status-indicator {
      width: 10px;
      height: 10px;
      border-radius: 50%;
      background-color: #ff4444;
    }

    .status-indicator.connected {
      background-color: #44ff44;
    }

    .metrics-bar {
      display: flex;
      gap: 30px;
      padding: 15px;
      background-color: #3d3d3d;
      border-radius: 6px;
      margin-bottom: 20px;
      flex-wrap: wrap;
    }

    .metric-item {
      display: flex;
      flex-direction: column;
      align-items: center;
      min-width: 100px;
    }

    .metric-label {
      font-size: 12px;
      color: #cccccc;
      text-transform: uppercase;
      letter-spacing: 0.5px;
      margin-bottom: 5px;
    }

    .metric-value {
      font-size: 16px;
      font-weight: 600;
      color: #ffffff;
    }

    .metric-value.positive {
      color: #44ff44;
    }

    .metric-value.negative {
      color: #ff4444;
    }

    .chart-wrapper {
      background-color: #1a1a1a;
      border-radius: 6px;
      padding: 10px;
    }

    .welcome-info {
      margin-top: 20px;
      padding: 15px;
      background-color: #3d3d3d;
      border-radius: 6px;
    }

    .welcome-info h3 {
      color: #ffffff;
      margin: 0 0 15px 0;
      font-size: 16px;
    }

    .condition-grid {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
      gap: 15px;
    }

    .condition-item {
      display: flex;
      justify-content: space-between;
      padding: 8px 0;
      border-bottom: 1px solid #555555;
    }

    .condition-label {
      font-size: 12px;
      color: #cccccc;
      text-transform: uppercase;
    }

    .condition-value {
      font-size: 14px;
      color: #ffffff;
      font-weight: 500;
    }

    .error-message {
      background-color: #4d1f1f;
      border: 1px solid #ff4444;
      border-radius: 6px;
      padding: 15px;
      margin: 20px 0;
      color: #ff9999;
    }
  `]
})
export class CandlestickChartComponent implements OnInit, OnDestroy {
  @ViewChild('chart') chart!: ChartComponent;
  
  private destroy$ = new Subject<void>();
  
  public chartOptions!: ChartOptions;
  public isConnected = false;
  public metrics: ChartMetrics | null = null;
  public welcomeData: WelcomeData | null = null;
  public currentSymbol: string | null = null;
  public errorMessage: string | null = null;

  constructor(
    private webSocketService: WebSocketService,
    private chartService: ChartService
  ) {}

  ngOnInit(): void {
    this.initializeSubscriptions();
  }

  ngOnDestroy(): void {
    this.destroy$.next();
    this.destroy$.complete();
    this.webSocketService.disconnect();
  }

  private initializeSubscriptions(): void {
    // Connection status
    this.webSocketService.connectionStatus$
      .pipe(takeUntil(this.destroy$))
      .subscribe(connected => {
        this.isConnected = connected;
        if (!connected) {
          this.errorMessage = 'WebSocket disconnected. Attempting to reconnect...';
        } else {
          this.errorMessage = null;
        }
      });

    // Chart options
    this.chartService.chartOptions$
      .pipe(takeUntil(this.destroy$))
      .subscribe(options => {
        this.chartOptions = options;
      });

    // Chart metrics
    this.chartService.chartMetrics$
      .pipe(takeUntil(this.destroy$))
      .subscribe(metrics => {
        this.metrics = metrics;
        if (metrics) {
          this.currentSymbol = metrics.symbol;
        }
      });

    // Adaptive bars
    this.webSocketService.adaptiveBars$
      .pipe(takeUntil(this.destroy$))
      .subscribe(barData => {
        this.chartService.addAdaptiveBar(barData);
      });

    // Welcome data
    this.webSocketService.welcomeData$
      .pipe(takeUntil(this.destroy$))
      .subscribe(welcomeData => {
        this.welcomeData = welcomeData;
      });

    // Errors
    this.webSocketService.errors$
      .pipe(takeUntil(this.destroy$))
      .subscribe(error => {
        this.errorMessage = error;
      });
  }

  public getPriceChangeClass(): string {
    if (!this.metrics) return '';
    return this.metrics.priceChange > 0 ? 'positive' : 
           this.metrics.priceChange < 0 ? 'negative' : '';
  }

  public formatPriceChange(): string {
    if (!this.metrics) return '';
    const change = this.metrics.priceChange;
    const percent = this.metrics.priceChangePercent;
    const sign = change >= 0 ? '+' : '';
    return `${sign}${change.toFixed(2)} (${sign}${percent.toFixed(2)}%)`;
  }

  public formatVolume(): string {
    if (!this.metrics) return '';
    const volume = this.metrics.volume;
    if (volume >= 1000000) {
      return (volume / 1000000).toFixed(2) + 'M';
    } else if (volume >= 1000) {
      return (volume / 1000).toFixed(2) + 'K';
    }
    return volume.toFixed(2);
  }

  public addSampleData(): void {
    console.log('Adding sample data for testing...');
    this.chartService.addSampleData();
  }
}
