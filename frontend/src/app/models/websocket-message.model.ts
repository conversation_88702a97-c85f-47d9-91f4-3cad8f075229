export interface WebSocketMessage {
  type: string;
  timestamp: string;
  symbol: string;
  data: any;
}

export interface AdaptiveBarData {
  openTime: string;
  closeTime: string;
  symbol: string;
  openPrice: number;
  highPrice: number;
  lowPrice: number;
  closePrice: number;
  volumeWeightedAveragePrice: number;
  totalVolumeTraded: number;
  aggressiveBuyVolume: number;
  aggressiveSellVolume: number;
  tradeCount: number;
  closingReason: string;
  marketConditionSnapshot?: MarketConditionSnapshot;
}

export interface MarketConditionSnapshot {
  rollingVolatility: number;
  tradesPerSecond: number;
  recentVolumeRate: number;
  dynamicVolumeThreshold: number;
  baseVolumeThreshold: number;
  volatilityAdjustment: number;
  tpsAdjustment: number;
  volumeRateAdjustment: number;
  calculatedAt: string;
  volatilityWindowSize: number;
  tpsWindowDurationSeconds: number;
  volumeRateWindowSize: number;
}

export interface WelcomeData {
  message: string;
  aggregatorConfig: string;
  currentMarketConditions: MarketConditionSnapshot;
  currentBarStats: string;
}

export interface CandlestickData {
  x: Date;
  y: [number, number, number, number]; // [open, high, low, close]
}

export interface ChartMetrics {
  symbol: string;
  lastPrice: number;
  priceChange: number;
  priceChangePercent: number;
  volume: number;
  tradeCount: number;
  vwap: number;
  lastUpdate: Date;
}
