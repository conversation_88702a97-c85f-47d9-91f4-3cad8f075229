import { Injectable } from '@angular/core';
import { BehaviorSubject } from 'rxjs';
import { AdaptiveBarData, CandlestickData, ChartMetrics } from '../models/websocket-message.model';
import { ApexAxisChartSeries, ApexChart, ApexXAxis, ApexYAxis, ApexTooltip, ApexTheme, ApexGrid, ApexPlotOptions } from 'ng-apexcharts';

export type ChartOptions = {
  series: ApexAxisChartSeries;
  chart: ApexChart;
  xaxis: ApexXAxis;
  yaxis: ApexYAxis;
  tooltip: ApexTooltip;
  theme: ApexTheme;
  grid: ApexGrid;
  plotOptions: ApexPlotOptions;
};

@Injectable({
  providedIn: 'root'
})
export class ChartService {
  private candlestickData: CandlestickData[] = [];
  private maxDataPoints = 100; // Keep last 100 bars
  
  private chartMetricsSubject = new BehaviorSubject<ChartMetrics | null>(null);
  public chartMetrics$ = this.chartMetricsSubject.asObservable();
  
  private chartOptionsSubject = new BehaviorSubject<ChartOptions>(this.getInitialChartOptions());
  public chartOptions$ = this.chartOptionsSubject.asObservable();

  constructor() {}

  public addAdaptiveBar(barData: AdaptiveBarData): void {
    // Convert AdaptiveBarData to CandlestickData
    const candlestick: CandlestickData = {
      x: new Date(barData.closeTime),
      y: [
        barData.openPrice,
        barData.highPrice,
        barData.lowPrice,
        barData.closePrice
      ]
    };

    // Add new data point
    this.candlestickData.push(candlestick);
    
    // Keep only the last maxDataPoints
    if (this.candlestickData.length > this.maxDataPoints) {
      this.candlestickData = this.candlestickData.slice(-this.maxDataPoints);
    }

    // Update chart options with new data
    this.updateChartOptions();
    
    // Update metrics
    this.updateMetrics(barData);
  }

  private updateChartOptions(): void {
    const currentOptions = this.chartOptionsSubject.value;
    const updatedOptions: ChartOptions = {
      ...currentOptions,
      series: [{
        name: 'Price',
        data: this.candlestickData
      }]
    };
    
    this.chartOptionsSubject.next(updatedOptions);
  }

  private updateMetrics(barData: AdaptiveBarData): void {
    const previousMetrics = this.chartMetricsSubject.value;
    const previousPrice = previousMetrics?.lastPrice || barData.openPrice;
    
    const priceChange = barData.closePrice - previousPrice;
    const priceChangePercent = previousPrice > 0 ? (priceChange / previousPrice) * 100 : 0;

    const metrics: ChartMetrics = {
      symbol: barData.symbol,
      lastPrice: barData.closePrice,
      priceChange: priceChange,
      priceChangePercent: priceChangePercent,
      volume: barData.totalVolumeTraded,
      tradeCount: barData.tradeCount,
      vwap: barData.volumeWeightedAveragePrice,
      lastUpdate: new Date(barData.closeTime)
    };

    this.chartMetricsSubject.next(metrics);
  }

  private getInitialChartOptions(): ChartOptions {
    return {
      series: [{
        name: 'Price',
        data: []
      }],
      chart: {
        type: 'candlestick',
        height: 500,
        background: 'transparent',
        foreColor: '#ffffff',
        animations: {
          enabled: true,
          speed: 800,
          animateGradually: {
            enabled: true,
            delay: 150
          },
          dynamicAnimation: {
            enabled: true,
            speed: 350
          }
        },
        toolbar: {
          show: true,
          tools: {
            download: true,
            selection: true,
            zoom: true,
            zoomin: true,
            zoomout: true,
            pan: true,
            reset: true
          }
        }
      },
      xaxis: {
        type: 'datetime',
        labels: {
          style: {
            colors: '#ffffff'
          }
        },
        axisBorder: {
          color: '#404040'
        },
        axisTicks: {
          color: '#404040'
        }
      },
      yaxis: {
        tooltip: {
          enabled: true
        },
        labels: {
          style: {
            colors: '#ffffff'
          },
          formatter: (value: number) => {
            return value.toFixed(2);
          }
        }
      },
      tooltip: {
        theme: 'dark',
        style: {
          fontSize: '12px',
          fontFamily: 'Segoe UI, sans-serif'
        }
      },
      theme: {
        mode: 'dark'
      },
      grid: {
        borderColor: '#404040',
        strokeDashArray: 3
      },
      plotOptions: {
        candlestick: {
          colors: {
            upward: '#00ff88',
            downward: '#ff4444'
          },
          wick: {
            useFillColor: true
          }
        }
      }
    };
  }

  public clearData(): void {
    this.candlestickData = [];
    this.updateChartOptions();
    this.chartMetricsSubject.next(null);
  }

  public getCurrentData(): CandlestickData[] {
    return [...this.candlestickData];
  }
}
