import { Injectable } from '@angular/core';
import { BehaviorSubject, Observable, Subject } from 'rxjs';
import { WebSocketMessage, AdaptiveBarData, WelcomeData } from '../models/websocket-message.model';
import { environment } from '../../environments/environment';

@Injectable({
  providedIn: 'root'
})
export class WebSocketService {
  private socket: WebSocket | null = null;
  private readonly wsUrl = environment.websocketUrl;
  
  // Subjects for different message types
  private connectionStatus = new BehaviorSubject<boolean>(false);
  private adaptiveBarSubject = new Subject<AdaptiveBarData>();
  private welcomeDataSubject = new Subject<WelcomeData>();
  private errorSubject = new Subject<string>();
  
  // Public observables
  public connectionStatus$ = this.connectionStatus.asObservable();
  public adaptiveBars$ = this.adaptiveBarSubject.asObservable();
  public welcomeData$ = this.welcomeDataSubject.asObservable();
  public errors$ = this.errorSubject.asObservable();

  constructor() {
    this.connect();
  }

  private connect(): void {
    try {
      this.socket = new WebSocket(this.wsUrl);
      
      this.socket.onopen = (event) => {
        console.log('WebSocket connected:', event);
        this.connectionStatus.next(true);
      };

      this.socket.onmessage = (event) => {
        try {
          const message: WebSocketMessage = JSON.parse(event.data);
          this.handleMessage(message);
        } catch (error) {
          console.error('Error parsing WebSocket message:', error);
          this.errorSubject.next('Error parsing message from server');
        }
      };

      this.socket.onclose = (event) => {
        console.log('WebSocket disconnected:', event);
        this.connectionStatus.next(false);
        
        // Attempt to reconnect after 3 seconds
        setTimeout(() => {
          if (!this.connectionStatus.value) {
            console.log('Attempting to reconnect...');
            this.connect();
          }
        }, 3000);
      };

      this.socket.onerror = (error) => {
        console.error('WebSocket error:', error);
        this.errorSubject.next('WebSocket connection error');
        this.connectionStatus.next(false);
      };

    } catch (error) {
      console.error('Error creating WebSocket connection:', error);
      this.errorSubject.next('Failed to create WebSocket connection');
    }
  }

  private handleMessage(message: WebSocketMessage): void {
    console.log('Received message:', message);
    
    switch (message.type) {
      case 'ADAPTIVE_BAR':
        if (message.data) {
          this.adaptiveBarSubject.next(message.data as AdaptiveBarData);
        }
        break;
        
      case 'WELCOME':
        if (message.data) {
          this.welcomeDataSubject.next(message.data as WelcomeData);
        }
        break;
        
      case 'ERROR':
        this.errorSubject.next(message.data as string);
        break;
        
      case 'CONFIRMATION':
        console.log('Server confirmation:', message.data);
        break;
        
      default:
        console.log('Unknown message type:', message.type);
    }
  }

  public sendMessage(message: any): void {
    if (this.socket && this.socket.readyState === WebSocket.OPEN) {
      this.socket.send(JSON.stringify(message));
    } else {
      console.warn('WebSocket is not connected');
      this.errorSubject.next('Cannot send message: WebSocket not connected');
    }
  }

  public disconnect(): void {
    if (this.socket) {
      this.socket.close();
      this.socket = null;
    }
  }

  public isConnected(): boolean {
    return this.connectionStatus.value;
  }
}
