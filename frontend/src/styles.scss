/* Global Styles inspired by backtesting.py library */
* {
  box-sizing: border-box;
}

body {
  margin: 0;
  padding: 0;
  font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
  background-color: #1a1a1a;
  color: #ffffff;
  overflow-x: hidden;
}

.container {
  max-width: 100%;
  margin: 0 auto;
  padding: 20px;
}

.header {
  background-color: #2d2d2d;
  padding: 15px 20px;
  border-bottom: 1px solid #404040;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

.header h1 {
  margin: 0;
  color: #ffffff;
  font-size: 24px;
  font-weight: 600;
}

.status-bar {
  background-color: #333333;
  padding: 10px 20px;
  border-bottom: 1px solid #404040;
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 14px;
}

.status-item {
  display: flex;
  align-items: center;
  gap: 8px;
}

.status-indicator {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  background-color: #ff4444;
}

.status-indicator.connected {
  background-color: #44ff44;
}

.chart-container {
  background-color: #2d2d2d;
  border-radius: 8px;
  padding: 20px;
  margin: 20px 0;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.3);
}

.chart-title {
  color: #ffffff;
  font-size: 18px;
  font-weight: 600;
  margin-bottom: 15px;
  text-align: center;
}

.metrics-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 15px;
  margin-top: 20px;
}

.metric-card {
  background-color: #3d3d3d;
  padding: 15px;
  border-radius: 6px;
  border-left: 4px solid #007acc;
}

.metric-label {
  font-size: 12px;
  color: #cccccc;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  margin-bottom: 5px;
}

.metric-value {
  font-size: 18px;
  font-weight: 600;
  color: #ffffff;
}

.metric-change {
  font-size: 12px;
  margin-top: 5px;
}

.metric-change.positive {
  color: #44ff44;
}

.metric-change.negative {
  color: #ff4444;
}

.loading {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 400px;
  color: #cccccc;
  font-size: 16px;
}

.error {
  background-color: #4d1f1f;
  border: 1px solid #ff4444;
  border-radius: 6px;
  padding: 15px;
  margin: 20px 0;
  color: #ff9999;
}

/* ApexCharts customization */
.apexcharts-canvas {
  background: transparent !important;
}

.apexcharts-tooltip {
  background-color: #2d2d2d !important;
  border: 1px solid #404040 !important;
  color: #ffffff !important;
}

.apexcharts-tooltip-title {
  background-color: #404040 !important;
  color: #ffffff !important;
  border-bottom: 1px solid #555555 !important;
}

.apexcharts-xaxistooltip {
  background-color: #2d2d2d !important;
  border: 1px solid #404040 !important;
  color: #ffffff !important;
}

.apexcharts-yaxistooltip {
  background-color: #2d2d2d !important;
  border: 1px solid #404040 !important;
  color: #ffffff !important;
}
