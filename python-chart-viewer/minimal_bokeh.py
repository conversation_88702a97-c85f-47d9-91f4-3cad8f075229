#!/usr/bin/env python3
"""
Minimal Bokeh Chart Viewer - No datetime axes to avoid compatibility issues
"""

import json
import logging
import threading
import time
from datetime import datetime, timezone
from collections import deque
from typing import Dict, List, Optional

import pandas as pd
import numpy as np
import websocket
from flask import Flask, render_template_string

from bokeh.plotting import figure
from bokeh.embed import components
from bokeh.resources import CDN

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Flask app
app = Flask(__name__)

# Global data storage
bars_data = deque(maxlen=100)
connection_status = "Disconnected"

class JavaWebSocketClient:
    def __init__(self, url: str):
        self.url = url
        self.ws = None
        self.running = False
    
    def on_message(self, ws, message):
        global bars_data, connection_status
        try:
            data = json.loads(message)
            message_type = data.get('type')
            
            if message_type == 'ADAPTIVE_BAR':
                bar_data = data.get('data')
                if bar_data:
                    bars_data.append(bar_data)
                    logger.info(f"Added bar: {bar_data.get('symbol')} - Total bars: {len(bars_data)}")
            
            elif message_type == 'WELCOME':
                welcome_data = data.get('data', {})
                logger.info(f"Connected: {welcome_data.get('message')}")
                
        except Exception as e:
            logger.error(f"Error processing message: {e}")
    
    def on_error(self, ws, error):
        global connection_status
        logger.error(f"WebSocket error: {error}")
        connection_status = "Error"
    
    def on_close(self, ws, close_status_code, close_msg):
        global connection_status
        logger.info("WebSocket connection closed")
        connection_status = "Disconnected"
    
    def on_open(self, ws):
        global connection_status
        logger.info("WebSocket connection opened")
        connection_status = "Connected"
    
    def connect(self):
        if not self.running:
            return
        
        try:
            self.ws = websocket.WebSocketApp(
                self.url,
                on_open=self.on_open,
                on_message=self.on_message,
                on_error=self.on_error,
                on_close=self.on_close
            )
            
            logger.info(f"Connecting to: {self.url}")
            self.ws.run_forever()
            
        except Exception as e:
            logger.error(f"Failed to connect: {e}")
    
    def start(self):
        self.running = True
        thread = threading.Thread(target=self.connect, daemon=True)
        thread.start()
        return thread

def create_minimal_chart():
    """Create a minimal Bokeh chart without datetime axes."""
    global bars_data
    
    try:
        if not bars_data:
            # Empty chart
            p = figure(
                title="Adaptive Volume Bars - No Data",
                width=1000,
                height=400,
                tools=[]
            )
            p.text(x=[0], y=[0], text=["Waiting for data..."], text_align="center")
            logger.info("Created empty chart")
            return p
        
        # Convert to DataFrame
        df = pd.DataFrame(list(bars_data))
        logger.info(f"Creating chart with {len(df)} bars")
        
        # Convert prices to numeric
        for col in ['closePrice', 'totalVolumeTraded']:
            if col in df.columns:
                df[col] = pd.to_numeric(df[col], errors='coerce')
        
        # Use simple numeric x-axis (bar index) instead of datetime
        df['bar_index'] = range(len(df))
        
        logger.info(f"Price range: {df['closePrice'].min()} to {df['closePrice'].max()}")
        
        # Create simple chart with numeric axes only
        p = figure(
            title=f"Adaptive Volume Bars - {df['symbol'].iloc[-1]} ({len(df)} bars)",
            width=1000,
            height=400,
            tools=[],  # No tools to avoid compatibility issues
            x_axis_label="Bar Number",
            y_axis_label="Price"
        )
        
        # Simple line chart
        p.line(df['bar_index'], df['closePrice'], line_width=3, color='blue', alpha=0.8)
        p.circle(df['bar_index'], df['closePrice'], size=8, color='blue', alpha=0.6)
        
        # Style
        p.title.text_font_size = "16pt"
        p.title.text_color = "#333333"
        
        logger.info("Chart created successfully")
        return p
        
    except Exception as e:
        logger.error(f"Error creating chart: {e}")
        # Return error chart
        p = figure(title="Chart Error", width=1000, height=400, tools=[])
        p.text(x=[0], y=[0], text=[f"Error: {str(e)}"], text_align="center")
        return p

@app.route('/')
def index():
    """Main page with minimal Bokeh chart."""
    global bars_data, connection_status
    
    chart = create_minimal_chart()
    script, div = components(chart)
    
    html_template = """
    <!DOCTYPE html>
    <html>
    <head>
        <title>Minimal Bokeh Adaptive Bar Viewer</title>
        {{ bokeh_css|safe }}
        <style>
            body { 
                font-family: Arial, sans-serif; 
                margin: 20px; 
                background-color: #f8f9fa;
            }
            .header { 
                background: #e9ecef; 
                padding: 15px; 
                margin-bottom: 20px; 
                border-radius: 5px;
                border: 1px solid #dee2e6;
            }
            .status { 
                color: {{ 'green' if status == 'Connected' else 'red' }}; 
                font-weight: bold;
            }
            .chart-container {
                background: white;
                padding: 20px;
                border-radius: 5px;
                border: 1px solid #dee2e6;
                margin-top: 20px;
            }
            button {
                background: #007bff;
                color: white;
                border: none;
                padding: 8px 16px;
                border-radius: 4px;
                cursor: pointer;
            }
            button:hover {
                background: #0056b3;
            }
            .info {
                background: #d4edda;
                border: 1px solid #c3e6cb;
                padding: 10px;
                border-radius: 4px;
                margin: 10px 0;
            }
        </style>
    </head>
    <body>
        <div class="header">
            <h1>📊 Minimal Bokeh Adaptive Bar Viewer</h1>
            <p>Status: <span class="status">{{ status }}</span> | Bars: {{ bar_count }}</p>
            <button onclick="location.reload()">🔄 Refresh</button>
        </div>
        
        <div class="info">
            <strong>Chart Type:</strong> Simple line chart (no datetime axes)<br>
            <strong>X-Axis:</strong> Bar number (sequential)<br>
            <strong>Y-Axis:</strong> Close price<br>
            <strong>Note:</strong> This avoids Bokeh datetime compatibility issues
        </div>
        
        <div class="chart-container">
            <h3>Real-time Adaptive Volume Bars</h3>
            {{ div|safe }}
        </div>
        
        {{ bokeh_js|safe }}
        {{ script|safe }}
        
        <script>
            // Auto-refresh every 15 seconds
            setTimeout(function() {
                location.reload();
            }, 15000);
        </script>
    </body>
    </html>
    """
    
    return render_template_string(
        html_template,
        script=script,
        div=div,
        bokeh_css=CDN.render_css(),
        bokeh_js=CDN.render_js(),
        status=connection_status,
        bar_count=len(bars_data)
    )

@app.route('/debug')
def debug():
    """Debug endpoint."""
    global bars_data, connection_status
    
    return {
        'status': connection_status,
        'bar_count': len(bars_data),
        'sample_data': list(bars_data)[-3:] if bars_data else []
    }

if __name__ == '__main__':
    # Start WebSocket client
    logger.info("Starting minimal Bokeh chart viewer...")
    client = JavaWebSocketClient("ws://localhost:8080/ws/adaptive-bars")
    client.start()
    
    # Start Flask
    logger.info("Starting Flask server on http://localhost:5000")
    app.run(host='0.0.0.0', port=5000, debug=False)
