package dev.riza.cuan.strategy;

import dev.riza.cuan.aggregator.AdaptiveVolumeBarAggregator;
import dev.riza.cuan.aggregator.TimeBasedOHLCBarAggregator;
import dev.riza.cuan.config.IndicatorRegistry;
import dev.riza.cuan.domain.model.AdaptiveBarData;
import dev.riza.cuan.domain.model.OHLCCandle;
import jakarta.annotation.PostConstruct;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.integration.annotation.ServiceActivator;
import org.springframework.messaging.Message;
import org.springframework.stereotype.Component;

/**
 * Trading strategy implementation that demonstrates the usage of both
 * TimeBasedOHLCBarAggregator and AdaptiveVolumeBarAggregator
 * for generating different types of market data bars and processing market data.
 */
@Component
public class MyStrategy {

    private static final Logger logger = LoggerFactory.getLogger(MyStrategy.class);

    private final TimeBasedOHLCBarAggregator ohlcAggregator;
    private final AdaptiveVolumeBarAggregator adaptiveAggregator;
    private final IndicatorRegistry indicatorRegistry;

    @Autowired
    public MyStrategy(TimeBasedOHLCBarAggregator ohlcAggregator,
                     AdaptiveVolumeBarAggregator adaptiveAggregator,
                     IndicatorRegistry indicatorRegistry) {
        this.ohlcAggregator = ohlcAggregator;
        this.adaptiveAggregator = adaptiveAggregator;
        this.indicatorRegistry = indicatorRegistry;
    }

    @PostConstruct
    public void initialize() {
        // Register both aggregators with the indicator registry
//        indicatorRegistry.register(ohlcAggregator);
        indicatorRegistry.register(adaptiveAggregator);

        // Subscribe to OHLC candle updates
//        ohlcAggregator.getStream().subscribe(this::onCandleUpdate);

        // Subscribe to adaptive bar updates
        adaptiveAggregator.getStream().subscribe(this::onAdaptiveBarUpdate);

//        logger.info("MyStrategy initialized with OHLC aggregator: {}", ohlcAggregator.getConfigInfo());
        logger.info("MyStrategy initialized with Adaptive aggregator: {}", adaptiveAggregator.getConfigInfo());
    }

    /**
     * Handles OHLC candle updates from the aggregator.
     * This method is called for both completed and incomplete candles.
     *
     * @param candle The OHLC candle (completed or incomplete)
     */
    private void onCandleUpdate(OHLCCandle candle) {
        if (candle.isComplete()) {
            onCompletedCandle(candle);
        } else {
            onIncompleteCandle(candle);
        }
    }

    /**
     * Processes completed OHLC candles for trading decisions.
     *
     * @param candle The completed OHLC candle
     */
    private void onCompletedCandle(OHLCCandle candle) {
        logger.info("Completed Candle [{}]: {} | OHLC: {}/{}/{}/{} | Volume: {} | Trades: {}",
                   candle.getTimeInterval(),
                   candle.getSymbol(),
                   candle.getOpen(), candle.getHigh(), candle.getLow(), candle.getClose(),
                   candle.getTotalVolume(),
                   candle.getTradeCount());

        // Example trading logic based on completed candles
        analyzeCandle(candle);
    }

    /**
     * Processes incomplete OHLC candles for real-time monitoring.
     *
     * @param candle The incomplete OHLC candle
     */
    private void onIncompleteCandle(OHLCCandle candle) {
        // Log incomplete candles less frequently to avoid spam
        if (candle.getTradeCount() % 10 == 0) {
            logger.info("Incomplete Candle [{}]: {} | Current: {} | Trades: {} | Volume: {}",
                        candle.getTimeInterval(),
                        candle.getSymbol(),
                        candle.getClose(),
                        candle.getTradeCount(),
                        candle.getTotalVolume());
        }
    }

    /**
     * Analyzes completed candles for trading opportunities.
     *
     * @param candle The completed candle to analyze
     */
    private void analyzeCandle(OHLCCandle candle) {
        // Example: Simple price movement analysis
        if (candle.getOpen() != null && candle.getClose() != null) {
            double priceChange = candle.getClose().subtract(candle.getOpen()).doubleValue();
            double priceChangePercent = (priceChange / candle.getOpen().doubleValue()) * 100;

            if (Math.abs(priceChangePercent) > 0.1) { // 0.1% threshold
                logger.info("Significant price movement detected: {}% in {} candle",
                           String.format("%.2f", priceChangePercent), candle.getTimeInterval());
            }
        }

        // Example: Volume analysis
        if (candle.getBuyVolume() != null && candle.getSellVolume() != null) {
            double buyRatio = candle.getBuyVolume().doubleValue() / candle.getTotalVolume().doubleValue();
            if (buyRatio > 0.7) {
                logger.info("Strong buying pressure detected: {}% buy volume", String.format("%.1f", buyRatio * 100));
            } else if (buyRatio < 0.3) {
                logger.info("Strong selling pressure detected: {}% sell volume", String.format("%.1f", (1 - buyRatio) * 100));
            }
        }
    }

    /**
     * Handles adaptive bar updates from the aggregator.
     *
     * @param adaptiveBar The completed adaptive bar
     */
    private void onAdaptiveBarUpdate(AdaptiveBarData adaptiveBar) {
        logger.info("Completed Adaptive Bar [{}]: {} | OHLC: {}/{}/{}/{} | Volume: {} | Trades: {} | Threshold: {} | TPS: {} | Volatility: {}",
                   adaptiveBar.getSymbol(),
                   adaptiveBar.getCloseTime(),
                   adaptiveBar.getOpenPrice(), adaptiveBar.getHighPrice(),
                   adaptiveBar.getLowPrice(), adaptiveBar.getClosePrice(),
                   adaptiveBar.getTotalVolumeTraded(),
                   adaptiveBar.getTradeCount(),
                   adaptiveBar.getMarketConditionSnapshotAtClose().getDynamicVolumeThreshold(),
                   adaptiveBar.getMarketConditionSnapshotAtClose().getTradesPerSecond(),
                   adaptiveBar.getMarketConditionSnapshotAtClose().getRollingVolatility());

        // Example adaptive bar analysis
        analyzeAdaptiveBar(adaptiveBar);
    }

    /**
     * Analyzes completed adaptive bars for trading opportunities.
     *
     * @param adaptiveBar The completed adaptive bar to analyze
     */
    private void analyzeAdaptiveBar(AdaptiveBarData adaptiveBar) {
        AdaptiveBarData.MarketConditionSnapshot conditions = adaptiveBar.getMarketConditionSnapshotAtClose();

        // Example: Market condition analysis
        if (conditions.getTradesPerSecond() > 5.0) {
            logger.info("High trading activity detected: {} TPS", conditions.getTradesPerSecond());
        }

        if (conditions.getRollingVolatility().doubleValue() > 100.0) {
            logger.info("High volatility detected: {}", conditions.getRollingVolatility());
        }

        // Example: Volume imbalance analysis
        double buyRatio = adaptiveBar.getAggressiveBuyVolume().doubleValue() /
                         adaptiveBar.getTotalVolumeTraded().doubleValue();

        if (buyRatio > 0.7) {
            logger.info("Strong buying pressure in adaptive bar: {}% aggressive buy volume", buyRatio * 100);
        } else if (buyRatio < 0.3) {
            logger.info("Strong selling pressure in adaptive bar: {}% aggressive sell volume", (1 - buyRatio) * 100);
        }

        // Example: Threshold efficiency analysis
        double thresholdEfficiency = adaptiveBar.getTotalVolumeTraded().doubleValue() /
                                   conditions.getDynamicVolumeThreshold().doubleValue();

        if (thresholdEfficiency > 1.2) {
            logger.info("Bar exceeded threshold significantly: {}% of threshold", thresholdEfficiency * 100);
        }
    }

    /**
     * Gets statistics about the current strategy state.
     *
     * @return Statistics string
     */
    public String getStrategyStatistics() {
        return String.format("MyStrategy Stats: OHLC[%s] | Adaptive[%s]",
                           ohlcAggregator.getStatistics(),
                           adaptiveAggregator.getCurrentBarStatistics());
    }

    /**
     * Gets current market conditions from the adaptive aggregator.
     *
     * @return Current market condition snapshot
     */
    public AdaptiveBarData.MarketConditionSnapshot getCurrentMarketConditions() {
        return adaptiveAggregator.getCurrentMarketConditions();
    }
}
